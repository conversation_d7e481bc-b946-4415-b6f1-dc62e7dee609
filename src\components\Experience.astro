---
// Experience component - Línea de tiempo vertical con experiencia laboral y educativa
---

<section id="experience" class="py-20 bg-white dark:bg-gray-900">
  <div class="container mx-auto px-6">
    <div class="max-w-4xl mx-auto">
      <!-- Section title -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Experiencia
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
      </div>

      <!-- Timeline -->
      <div class="relative">
        <!-- Timeline line -->
        <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-gradient-to-b from-blue-500 to-purple-500"></div>

        <!-- Experience items -->
        <div class="space-y-12">
          <!-- Work Experience -->
          <div class="timeline-item group relative">
            <!-- Timeline dot -->
            <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full border-4 border-white dark:border-gray-900 group-hover:scale-125 transition-transform duration-300 z-10"></div>
            
            <!-- Content -->
            <div class="ml-16 md:ml-0 md:grid md:grid-cols-2 md:gap-8 items-center">
              <!-- Left side (desktop) / Content (mobile) -->
              <div class="md:text-right md:pr-8">
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                  <div class="flex items-center justify-between mb-3">
                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-sm font-medium rounded-full">
                      Trabajo
                    </span>
                    <span class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                      Marzo 2025 - Junio 2025
                    </span>
                  </div>
                  
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Fullstack Developer
                  </h3>
                  <p class="text-blue-600 dark:text-blue-400 font-semibold mb-4">
                    SURLABS
                  </p>
                  
                  <!-- Expandable content -->
                  <div class="experience-details">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                      Desarrollo de soluciones innovadoras para plataformas eLearning
                    </p>
                    
                    <div class="experience-expanded hidden">
                      <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Responsabilidades:</h4>
                      <ul class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Desarrollo de plugins personalizados para la plataforma ILIAS
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Mejora de la experiencia de aprendizaje en entornos eLearning
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Implementación de APIs RESTful
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Gestión de bases de datos SQL
                        </li>
                      </ul>
                    </div>
                    
                    <button class="expand-btn mt-3 text-blue-600 dark:text-blue-400 text-sm font-medium hover:underline focus:outline-none">
                      Ver más detalles
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Right side spacer (desktop only) -->
              <div class="hidden md:block"></div>
            </div>
          </div>

          <!-- Education -->
          <div class="timeline-item group relative">
            <!-- Timeline dot -->
            <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-4 border-white dark:border-gray-900 group-hover:scale-125 transition-transform duration-300 z-10"></div>
            
            <!-- Content -->
            <div class="ml-16 md:ml-0 md:grid md:grid-cols-2 md:gap-8 items-center">
              <!-- Left side spacer (desktop only) -->
              <div class="hidden md:block"></div>
              
              <!-- Right side (desktop) / Content (mobile) -->
              <div class="md:pl-8">
                <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200/50 dark:border-purple-700/50 hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                  <div class="flex items-center justify-between mb-3">
                    <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300 text-sm font-medium rounded-full">
                      Educación
                    </span>
                    <span class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                      Sep 2023 - Jun 2025
                    </span>
                  </div>
                  
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Desarrollo de Aplicaciones Web
                  </h3>
                  <p class="text-purple-600 dark:text-purple-400 font-semibold mb-4">
                    IES Hermanos Machado
                  </p>
                  
                  <!-- Expandable content -->
                  <div class="experience-details">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                      Formación integral en desarrollo web moderno y tecnologías emergentes
                    </p>
                    
                    <div class="experience-expanded hidden">
                      <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Áreas de estudio:</h4>
                      <ul class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Desarrollo Frontend con React y TypeScript
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Desarrollo Backend con Node.js y PHP
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Bases de datos relacionales y NoSQL
                        </li>
                        <li class="flex items-start">
                          <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          Metodologías ágiles y control de versiones
                        </li>
                      </ul>
                    </div>
                    
                    <button class="expand-btn mt-3 text-purple-600 dark:text-purple-400 text-sm font-medium hover:underline focus:outline-none">
                      Ver más detalles
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle expand/collapse functionality
    const expandButtons = document.querySelectorAll('.expand-btn');
    
    expandButtons.forEach(button => {
      button.addEventListener('click', function() {
        const details = this.parentElement;
        const expandedContent = details.querySelector('.experience-expanded');
        
        if (expandedContent.classList.contains('hidden')) {
          expandedContent.classList.remove('hidden');
          this.textContent = 'Ver menos detalles';
        } else {
          expandedContent.classList.add('hidden');
          this.textContent = 'Ver más detalles';
        }
      });
    });

    // Intersection Observer for timeline animations
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    timelineItems.forEach(item => {
      item.style.opacity = '0';
      item.style.transform = 'translateY(30px)';
      item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(item);
    });
  });
</script>
