@import "tailwindcss";

/* Custom CSS for accessibility and responsiveness */

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gradient-to-r {
    background: theme('colors.blue.600') !important;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: theme('colors.gray.100');
}

.dark ::-webkit-scrollbar-track {
  background: theme('colors.gray.800');
}

::-webkit-scrollbar-thumb {
  background: theme('colors.gray.400');
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: theme('colors.gray.600');
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.500');
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.500');
}

/* Skip to main content link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: theme('colors.blue.600');
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Ensure text remains visible during webfont load */
@font-face {
  font-display: swap;
}

/* Better line height for readability */
p, li {
  line-height: 1.6;
}

/* Ensure minimum touch target size on mobile */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}