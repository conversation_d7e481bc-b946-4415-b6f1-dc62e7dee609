---
// Contact component - Formulario de contacto con validación
---

<section id="contact" class="py-20 bg-white dark:bg-gray-900">
  <div class="container mx-auto px-6">
    <div class="max-w-4xl mx-auto">
      <!-- Section title -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Hablemos
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
        <p class="mt-6 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          ¿Tienes un proyecto en mente? Me encantaría escuchar tus ideas y colaborar contigo.
        </p>
      </div>

      <div class="grid lg:grid-cols-2 gap-12">
        <!-- Contact Information -->
        <div class="space-y-8">
          <div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Información de Contacto
            </h3>
            <div class="space-y-6">
              <!-- Email -->
              <div class="flex items-center space-x-4 group">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Email</p>
                  <a 
                    href="mailto:<EMAIL>" 
                    class="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <!-- Phone -->
              <div class="flex items-center space-x-4 group">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Teléfono</p>
                  <a 
                    href="tel:+34651150308" 
                    class="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                  >
                    +34 651 150 308
                  </a>
                </div>
              </div>

              <!-- Location -->
              <div class="flex items-center space-x-4 group">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Ubicación</p>
                  <p class="text-lg font-semibold text-gray-900 dark:text-white">Sevilla, España</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Social Links -->
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sígueme en</h4>
            <div class="flex space-x-4">
              <a 
                href="#" 
                class="group w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 hover:shadow-lg"
                aria-label="LinkedIn"
              >
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>

              <a 
                href="#" 
                class="group w-12 h-12 bg-gradient-to-r from-gray-700 to-gray-900 rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 hover:shadow-lg"
                aria-label="GitHub"
              >
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Response time -->
          <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-2xl border border-blue-200/50 dark:border-blue-700/50">
            <div class="flex items-center space-x-3 mb-3">
              <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">Disponible para nuevos proyectos</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Tiempo de respuesta típico: <span class="font-semibold">24 horas</span>
            </p>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="bg-gray-50 dark:bg-gray-800/50 p-8 rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
          <form id="contact-form" class="space-y-6">
            <!-- Name -->
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nombre *
              </label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                placeholder="Tu nombre completo"
              >
              <div class="error-message hidden text-red-500 text-sm mt-1"></div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email *
              </label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                placeholder="<EMAIL>"
              >
              <div class="error-message hidden text-red-500 text-sm mt-1"></div>
            </div>

            <!-- Message -->
            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mensaje *
              </label>
              <textarea 
                id="message" 
                name="message" 
                rows="5" 
                required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-none"
                placeholder="Cuéntame sobre tu proyecto..."
              ></textarea>
              <div class="error-message hidden text-red-500 text-sm mt-1"></div>
            </div>

            <!-- Submit Button -->
            <button 
              type="submit" 
              class="w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              <span class="submit-text">Enviar Mensaje</span>
              <span class="loading-text hidden">Enviando...</span>
            </button>

            <!-- Success/Error Messages -->
            <div id="form-success" class="hidden p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-green-700 dark:text-green-300">¡Mensaje enviado correctamente! Te responderé pronto.</span>
              </div>
            </div>

            <div id="form-error" class="hidden p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <span class="text-red-700 dark:text-red-300">Hubo un error al enviar el mensaje. Por favor, inténtalo de nuevo.</span>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contact-form');
    const submitButton = form.querySelector('button[type="submit"]');
    const submitText = submitButton.querySelector('.submit-text');
    const loadingText = submitButton.querySelector('.loading-text');
    const successMessage = document.getElementById('form-success');
    const errorMessage = document.getElementById('form-error');

    // Form validation
    function validateField(field) {
      const value = field.value.trim();
      const errorElement = field.parentElement.querySelector('.error-message');
      let isValid = true;
      let errorText = '';

      // Clear previous error
      errorElement.classList.add('hidden');
      field.classList.remove('border-red-500');

      switch (field.type) {
        case 'text':
          if (value.length < 2) {
            errorText = 'El nombre debe tener al menos 2 caracteres';
            isValid = false;
          }
          break;
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            errorText = 'Por favor, introduce un email válido';
            isValid = false;
          }
          break;
        default:
          if (field.tagName === 'TEXTAREA' && value.length < 10) {
            errorText = 'El mensaje debe tener al menos 10 caracteres';
            isValid = false;
          }
      }

      if (!isValid) {
        errorElement.textContent = errorText;
        errorElement.classList.remove('hidden');
        field.classList.add('border-red-500');
      }

      return isValid;
    }

    // Real-time validation
    const fields = form.querySelectorAll('input, textarea');
    fields.forEach(field => {
      field.addEventListener('blur', () => validateField(field));
      field.addEventListener('input', () => {
        if (field.classList.contains('border-red-500')) {
          validateField(field);
        }
      });
    });

    // Form submission
    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      // Hide previous messages
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');

      // Validate all fields
      let isFormValid = true;
      fields.forEach(field => {
        if (!validateField(field)) {
          isFormValid = false;
        }
      });

      if (!isFormValid) {
        return;
      }

      // Show loading state
      submitButton.disabled = true;
      submitText.classList.add('hidden');
      loadingText.classList.remove('hidden');

      try {
        // Simulate form submission (replace with actual endpoint)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // For now, we'll just show success message
        // In a real implementation, you would send the data to your backend
        const formData = new FormData(form);
        console.log('Form data:', Object.fromEntries(formData));

        // Show success message
        successMessage.classList.remove('hidden');
        form.reset();

        // Scroll to success message
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

      } catch (error) {
        console.error('Error submitting form:', error);
        errorMessage.classList.remove('hidden');
      } finally {
        // Reset button state
        submitButton.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
      }
    });

    // Intersection Observer for contact section animation
    const contactSection = document.getElementById('contact');
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    if (contactSection) {
      contactSection.style.opacity = '0';
      contactSection.style.transform = 'translateY(30px)';
      contactSection.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(contactSection);
    }
  });
</script>
