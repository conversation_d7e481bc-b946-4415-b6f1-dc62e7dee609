---
// Hero component - Sección principal del portfolio
---

<section id="hero" class="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
  <!-- Background animated elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-400/5 rounded-full blur-3xl animate-spin-slow"></div>
  </div>

  <!-- Main content -->
  <div class="container mx-auto px-6 text-center relative z-10">
    <div class="max-w-4xl mx-auto">
      <!-- Animated greeting -->
      <div class="mb-8 opacity-0 animate-fade-in-up">
        <span class="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium tracking-wide uppercase">
          Desarrollador Fullstack
        </span>
      </div>

      <!-- Main title with gradient -->
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 opacity-0 animate-fade-in-up delay-200">
        <span class="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-300 dark:to-purple-300 bg-clip-text text-transparent">
          Guillermo
        </span>
        <br>
        <span class="bg-gradient-to-r from-purple-800 via-blue-800 to-gray-900 dark:from-purple-300 dark:via-blue-300 dark:to-white bg-clip-text text-transparent">
          Álvarez
        </span>
      </h1>

      <!-- Subtitle -->
      <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed opacity-0 animate-fade-in-up delay-400">
        Creando experiencias web modernas y fluidas con 
        <span class="text-blue-600 dark:text-blue-400 font-semibold">React</span>, 
        <span class="text-purple-600 dark:text-purple-400 font-semibold">TypeScript</span> y 
        <span class="text-indigo-600 dark:text-indigo-400 font-semibold">Astro</span>
      </p>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center opacity-0 animate-fade-in-up delay-600">
        <button 
          onclick="document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })"
          class="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-full overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25"
        >
          <span class="relative z-10">Contacta conmigo</span>
          <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </button>
        
        <button 
          onclick="document.getElementById('projects').scrollIntoView({ behavior: 'smooth' })"
          class="group px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-full transition-all duration-300 hover:border-blue-500 hover:text-blue-600 dark:hover:text-blue-400 hover:scale-105"
        >
          <span class="flex items-center gap-2">
            Ver Proyectos
            <svg class="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </span>
        </button>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 animate-fade-in-up delay-1000">
        <div class="flex flex-col items-center text-gray-400 dark:text-gray-500">
          <span class="text-sm mb-2 tracking-wide">Scroll para explorar</span>
          <div class="w-6 h-10 border-2 border-gray-300 dark:border-gray-600 rounded-full flex justify-center">
            <div class="w-1 h-3 bg-gray-400 dark:bg-gray-500 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes spin-slow {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animate-spin-slow {
    animation: spin-slow 20s linear infinite;
  }

  .delay-200 {
    animation-delay: 0.2s;
  }

  .delay-400 {
    animation-delay: 0.4s;
  }

  .delay-600 {
    animation-delay: 0.6s;
  }

  .delay-1000 {
    animation-delay: 1s;
  }
</style>
