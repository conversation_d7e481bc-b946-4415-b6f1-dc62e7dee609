---
// ThemeToggle component - Toggle para cambiar entre tema claro y oscuro
---

<div class="fixed top-20 right-6 z-40">
  <button
    id="theme-toggle"
    class="group p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
    aria-label="Toggle theme"
  >
    <!-- Sun icon (visible in dark mode) -->
    <svg
      id="sun-icon"
      class="w-5 h-5 text-yellow-500 transition-all duration-300 rotate-0 scale-100 dark:rotate-90 dark:scale-0"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
      />
    </svg>
    
    <!-- Moon icon (visible in light mode) -->
    <svg
      id="moon-icon"
      class="absolute top-3 left-3 w-5 h-5 text-gray-700 transition-all duration-300 rotate-90 scale-0 dark:rotate-0 dark:scale-100"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
      />
    </svg>
  </button>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.getElementById('theme-toggle');
    const html = document.documentElement;
    
    // Get initial theme
    let currentTheme = localStorage.getItem('theme') || 'light';
    
    // Apply initial theme
    html.classList.toggle('dark', currentTheme === 'dark');
    
    // Theme toggle functionality
    if (themeToggle) {
      themeToggle.addEventListener('click', function() {
        const isDark = html.classList.contains('dark');
        
        if (isDark) {
          html.classList.remove('dark');
          localStorage.setItem('theme', 'light');
          currentTheme = 'light';
        } else {
          html.classList.add('dark');
          localStorage.setItem('theme', 'dark');
          currentTheme = 'dark';
        }
        
        // Add a subtle animation effect
        themeToggle.style.transform = 'scale(0.95)';
        setTimeout(() => {
          themeToggle.style.transform = 'scale(1)';
        }, 150);
      });
    }
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', function(e) {
      // Only apply system preference if user hasn't manually set a theme
      if (!localStorage.getItem('theme')) {
        html.classList.toggle('dark', e.matches);
      }
    });
  });
</script>

<style>
  #theme-toggle {
    transition: transform 0.2s ease;
  }
  
  #theme-toggle:active {
    transform: scale(0.95);
  }
</style>
