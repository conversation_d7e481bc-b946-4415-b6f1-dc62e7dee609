---
// Languages component - Sección de idiomas con barras de progreso
---

<section id="languages" class="py-20 bg-gray-50 dark:bg-gray-800/50">
  <div class="container mx-auto px-6">
    <div class="max-w-4xl mx-auto">
      <!-- Section title -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Idiomas
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
        <p class="mt-6 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Comunicación efectiva en múltiples idiomas para proyectos internacionales
        </p>
      </div>

      <!-- Languages grid -->
      <div class="grid md:grid-cols-2 gap-8">
        <!-- Spanish -->
        <div class="language-item bg-white dark:bg-gray-900/50 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <!-- Spanish flag -->
              <div class="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div class="w-full h-4 bg-red-500"></div>
                <div class="w-full h-4 bg-yellow-400"></div>
                <div class="w-full h-4 bg-red-500"></div>
              </div>
              <div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Español</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Idioma nativo</p>
              </div>
            </div>
            <div class="text-right">
              <span class="text-2xl font-bold text-green-600 dark:text-green-400">100%</span>
              <p class="text-sm text-gray-500 dark:text-gray-400">Nativo</p>
            </div>
          </div>
          
          <!-- Progress bar -->
          <div class="relative">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
              <div class="progress-bar h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full transition-all duration-1000 ease-out" data-width="100"></div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
              <span>Básico</span>
              <span>Intermedio</span>
              <span>Avanzado</span>
              <span>Nativo</span>
            </div>
          </div>

          <!-- Skills breakdown -->
          <div class="mt-6 grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Habla</div>
              <div class="flex justify-center">
                <div class="flex space-x-1">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </div>
            <div class="text-center">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Escritura</div>
              <div class="flex justify-center">
                <div class="flex space-x-1">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- English -->
        <div class="language-item bg-white dark:bg-gray-900/50 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <!-- UK flag -->
              <div class="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-700 flex-shrink-0 relative bg-blue-600">
                <!-- Union Jack simplified -->
                <div class="absolute inset-0 bg-blue-600"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="w-full h-1 bg-white"></div>
                </div>
                <div class="absolute inset-0 flex justify-center items-center">
                  <div class="w-1 h-full bg-white"></div>
                </div>
                <div class="absolute top-0 left-0 w-6 h-6 bg-red-500 clip-triangle"></div>
                <div class="absolute top-0 right-0 w-6 h-6 bg-red-500 clip-triangle transform rotate-90"></div>
                <div class="absolute bottom-0 right-0 w-6 h-6 bg-red-500 clip-triangle transform rotate-180"></div>
                <div class="absolute bottom-0 left-0 w-6 h-6 bg-red-500 clip-triangle transform rotate-270"></div>
              </div>
              <div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Inglés</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Segunda lengua</p>
              </div>
            </div>
            <div class="text-right">
              <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">75%</span>
              <p class="text-sm text-gray-500 dark:text-gray-400">Conversacional</p>
            </div>
          </div>
          
          <!-- Progress bar -->
          <div class="relative">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
              <div class="progress-bar h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-1000 ease-out" data-width="75"></div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
              <span>Básico</span>
              <span>Intermedio</span>
              <span>Avanzado</span>
              <span>Nativo</span>
            </div>
          </div>

          <!-- Skills breakdown -->
          <div class="mt-6 grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Habla</div>
              <div class="flex justify-center">
                <div class="flex space-x-1">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                </div>
              </div>
            </div>
            <div class="text-center">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Escritura</div>
              <div class="flex justify-center">
                <div class="flex space-x-1">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div class="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional info -->
          <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 class="text-sm font-semibold text-blue-800 dark:text-blue-300 mb-2">Certificaciones</h4>
            <ul class="text-sm text-blue-700 dark:text-blue-400 space-y-1">
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                Nivel B2 - Intermedio Alto
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                Experiencia en documentación técnica
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Learning goals -->
      <div class="mt-12 text-center">
        <div class="bg-white dark:bg-gray-900/50 p-6 rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Objetivos de Aprendizaje
          </h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Continuamente mejorando mis habilidades lingüísticas para una comunicación más efectiva en proyectos internacionales.
          </p>
          <div class="flex flex-wrap justify-center gap-3">
            <span class="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">
              📚 Inglés Técnico Avanzado
            </span>
            <span class="px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-full text-sm font-medium">
              🎯 Certificación C1
            </span>
            <span class="px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">
              🌍 Comunicación Internacional
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Intersection Observer for language items animation
    const languageItems = document.querySelectorAll('.language-item');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
            
            // Animate progress bars
            const progressBar = entry.target.querySelector('.progress-bar');
            if (progressBar) {
              const width = progressBar.getAttribute('data-width');
              setTimeout(() => {
                progressBar.style.width = width + '%';
              }, 300);
            }
          }, index * 200);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    languageItems.forEach(item => {
      item.style.opacity = '0';
      item.style.transform = 'translateY(30px)';
      item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      
      // Initialize progress bars
      const progressBar = item.querySelector('.progress-bar');
      if (progressBar) {
        progressBar.style.width = '0%';
      }
      
      observer.observe(item);
    });
  });
</script>

<style>
  .clip-triangle {
    clip-path: polygon(0 0, 100% 0, 0 100%);
  }
</style>
